# 故障历史记录功能说明

## 概述

故障历史记录功能为电机驱动控制系统提供了完整的故障监控、记录、查询和分析能力。该功能能够自动监控设备状态变化，只在故障状态发生变化时记录数据，并提供多维度的查询和显示功能。

## 最新更新 (2025-10-08)

### 主要修改内容

#### 1. ✅ 数据库存储优化
- **二进制存储**: 将`registers_json`改为`registers_blob`，使用二进制BLOB存储寄存器数据
- **空间节省**: 每个uint32寄存器占4字节，相比JSON格式大幅减少存储空间
- **性能提升**: 二进制读写速度更快，查询效率更高
- **数据完整性**: 添加uint32范围检查，确保数据存储的正确性

#### 2. ✅ 表格列结构更新
更新为26列的详细显示结构：
```
日期时间, 设备名称, 报错A, 报错B, 报错C,
故障状态, 系统状态, 系统模式,
温度信息, mSpeed, angle, Vbus, Vbus_in, Id, Iq, Ud, Uq, Ia, Ib, Ic, Ibus,
mDuty, mPT1, mPT2, mPT3, mPT4, mPT5
```

#### 3. ✅ 时间显示精度提升
- **毫秒精度**: 时间显示格式从`YYYY-MM-DD HH:MM:SS`改为`YYYY-MM-DD HH:MM:SS.mmm`
- **高精度记录**: 支持毫秒级的故障时间记录

#### 4. ✅ 故障状态解析优化
- **参考实现**: 使用`ctrl_panel_frame.py`的`update_fault_status`方法解析逻辑
- **多行显示**: 不同错误类型换行显示，提高可读性
- **原始数据**: 直接使用寄存器原始值，通过`MotorControllerParser`解析

#### 5. ✅ 测试数据生成
- **自动插入**: 添加`insert_test_data()`方法，自动生成测试数据
- **测试UID**: 使用`0xAABBCCDD`作为测试设备UID
- **随机数据**: 生成10条包含故障和正常状态的随机测试记录

#### 6. ✅ 日历控件简化
- **暂时禁用**: 注释掉自定义`FaultCalendarPicker`的使用
- **默认控件**: 使用原有的`CalendarPicker`，后续手动替换

## 主要功能

### 1. 自动故障监控和记录
- **实时监控**: 监听系统寄存器上传数据（`sys_regs_uploaded`信号）
- **变化检测**: 只有当`error_code_u32`（寄存器63）或`flag1_uint32`（寄存器90）发生变化时才记录
- **首次记录**: 第一次收到设备数据时自动记录
- **完整数据**: 存储完整的寄存器数组，便于后续详细分析

### 2. 数据库存储
- **SQLite数据库**: 使用轻量级SQLite数据库存储故障记录
- **数据表结构**:
  - `id`: 记录唯一标识
  - `device_uid`: 设备UID
  - `timestamp_ms`: 时间戳（毫秒）
  - `error_code_u32`: 错误代码（寄存器63）
  - `flag1_uint32`: 故障标志（寄存器90）
  - `registers_json`: 完整寄存器数组（JSON格式）
  - `created_at`: 创建时间
- **索引优化**: 针对设备UID和时间戳建立索引，提高查询性能
- **去重机制**: 防止相同故障状态的重复记录

### 3. 设备筛选功能
- **设备下拉框**: 支持按设备UID筛选故障记录
- **所有设备选项**: 可查看所有设备的故障记录
- **设备名称显示**: 通过配置管理器获取设备友好名称
- **动态更新**: 新设备上线时自动更新设备列表

### 4. 日期筛选功能
- **日历选择器**: 使用自定义的故障日历选择器
- **故障日期标记**: 有故障数据的日期以红色背景显示
- **今日标记**: 今天的日期以蓝色背景显示
- **故障+今日**: 今天有故障时以深红色背景显示
- **快速定位**: 用户可快速找到有故障记录的日期

### 5. 详细故障信息显示
- **表格显示**: 使用TableWidget显示故障记录
- **多列信息**:
  - 日期时间
  - 设备名称
  - 错误代码A/B/C（分模块显示）
  - 故障标志A/B/C/D（按类别分列）
  - 系统状态
  - 温度信息
  - 转速信息
  - 母线电压信息
  - 电流信息

### 6. 美观的故障显示
- **故障高亮**: 故障项目使用红色字体和浅红色背景
- **正常状态**: 正常项目使用绿色字体和浅绿色背景
- **加粗显示**: 故障项目使用加粗字体突出显示
- **项目符号**: 故障列表使用"•"符号美化显示

## 技术实现

### 1. 核心模块

#### FaultDatabaseManager
- 位置: `src/database/fault_database_manager.py`
- 功能: SQLite数据库管理，包括创建、插入、查询、索引等
- 主要方法:
  - `insert_fault_record()`: 插入故障记录
  - `query_fault_records()`: 查询故障记录
  - `get_device_uids()`: 获取设备UID列表
  - `get_fault_dates()`: 获取故障日期列表

#### FaultCalendarPicker
- 位置: `src/ui/fault_calendar_picker.py`
- 功能: 自定义日历选择器，支持故障日期标记
- 主要方法:
  - `set_fault_dates()`: 设置故障日期列表
  - `has_fault_on_date()`: 检查指定日期是否有故障
  - `refresh_display()`: 刷新日历显示

#### FaultHistoryPanelForm
- 位置: `src/window/fault_history_panel_frame.py`
- 功能: 故障历史面板主界面
- 主要方法:
  - `on_on_sys_regs_uploaded()`: 处理系统寄存器数据
  - `_refresh_fault_table()`: 刷新故障记录表格
  - `_update_calendar_fault_dates()`: 更新日历故障日期标记

### 2. 数据解析

#### MotorControllerParser
- 使用现有的电机控制器解析器
- 解析错误代码（`parse_error_code()`）
- 解析故障标志（`parse_fault_flags()`）
- 解析温度信息（`parse_temperature()`）

### 3. 信号连接
- 主窗口中已正确连接`sys_regs_upload`信号到故障历史面板
- 支持异步处理，不阻塞主界面

## 使用方法

### 1. 查看故障记录
1. 打开故障历史记录页面
2. 选择要查看的设备（或选择"所有设备"）
3. 在日历中选择日期（红色标记的日期有故障记录）
4. 查看表格中的详细故障信息

### 2. 故障分析
- **错误代码列**: 显示各模块的具体错误信息
- **故障标志列**: 显示不同类别的故障状态
- **系统状态**: 快速判断设备是否正常
- **相关信息**: 查看故障发生时的温度、转速、电压、电流等参数

### 3. 数据筛选
- **按设备筛选**: 选择特定设备查看其故障历史
- **按日期筛选**: 选择特定日期查看当天的故障记录
- **组合筛选**: 同时按设备和日期筛选

## 配置说明

### 数据库配置
- 数据库文件: `data/fault_history.db`
- 自动创建: 首次运行时自动创建数据库和表结构
- 备份建议: 定期备份数据库文件

### 设备名称配置
- 通过`ConfigManager.get_device_name(uid)`获取设备名称
- 可在配置文件中设置设备的友好名称

## 性能优化

### 1. 数据库优化
- 使用索引提高查询性能
- 去重机制避免重复记录
- 支持数据清理（保留指定天数的记录）

### 2. 界面优化
- 异步处理避免界面卡顿
- 智能刷新减少不必要的更新
- 表格自适应列宽

### 3. 内存优化
- 限制查询记录数量（默认1000条）
- 按需加载数据
- 及时释放资源

## 扩展功能

### 1. 数据导出
- 可扩展支持导出为CSV、Excel等格式
- 支持按条件导出特定数据

### 2. 统计分析
- 可添加故障统计图表
- 支持故障趋势分析

### 3. 报警功能
- 可扩展支持故障报警
- 支持邮件或短信通知

## 测试

运行测试脚本验证功能：
```bash
python test_fault_history.py
```

测试内容包括：
- 数据库管理器功能测试
- 故障日历选择器测试
- 故障历史面板集成测试

## 注意事项

1. **数据库位置**: 确保`data`目录存在且有写权限
2. **寄存器长度**: 确保寄存器数组长度足够（至少91个元素）
3. **信号连接**: 确保主窗口正确连接了`sys_regs_upload`信号
4. **配置管理**: 确保配置管理器正常工作，能够获取设备名称

## 故障排除

### 常见问题
1. **数据库创建失败**: 检查`data`目录权限
2. **故障记录不显示**: 检查信号连接和寄存器数据
3. **日历标记不显示**: 检查故障日期查询逻辑
4. **表格显示异常**: 检查数据解析和表格填充逻辑
