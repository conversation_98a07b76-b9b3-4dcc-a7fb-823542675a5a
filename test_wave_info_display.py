#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试波形信息显示功能
"""

import sys
import os
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_time_formatting():
    """测试时间格式化功能"""
    print("测试时间格式化功能...")
    
    # 模拟OscilloscopeFrame的时间格式化方法
    def format_time_display(iso_time_str: str) -> str:
        try:
            from datetime import datetime
            # 解析ISO格式时间
            dt = datetime.fromisoformat(iso_time_str.replace('Z', '+00:00'))
            # 格式化为更友好的显示
            return dt.strftime('%Y-%m-%d %H:%M:%S')
        except Exception:
            return iso_time_str
    
    # 测试不同的时间格式
    test_times = [
        datetime.now().isoformat(),
        "2024-01-15T14:30:25.123456",
        "2024-01-15T14:30:25Z",
        "Invalid time string"
    ]
    
    for time_str in test_times:
        formatted = format_time_display(time_str)
        print(f"原始: {time_str}")
        print(f"格式化: {formatted}")
        print("-" * 40)

def test_file_size_formatting():
    """测试文件大小格式化功能"""
    print("测试文件大小格式化功能...")
    
    # 模拟OscilloscopeFrame的文件大小格式化方法
    def format_file_size(size_bytes: int) -> str:
        if size_bytes < 1024:
            return f"{size_bytes} B"
        elif size_bytes < 1024 * 1024:
            return f"{size_bytes / 1024:.1f} KB"
        elif size_bytes < 1024 * 1024 * 1024:
            return f"{size_bytes / (1024 * 1024):.1f} MB"
        else:
            return f"{size_bytes / (1024 * 1024 * 1024):.1f} GB"
    
    # 测试不同的文件大小
    test_sizes = [
        512,           # 512 B
        1536,          # 1.5 KB
        2097152,       # 2 MB
        1073741824,    # 1 GB
        5368709120     # 5 GB
    ]
    
    for size in test_sizes:
        formatted = format_file_size(size)
        print(f"字节数: {size:,}")
        print(f"格式化: {formatted}")
        print("-" * 40)

def test_wave_info_display():
    """测试波形信息显示格式"""
    print("测试波形信息显示格式...")
    
    # 模拟录制状态的信息显示
    current_file_name = "test_waveform_20241208_143025123.h5"
    created_time = "2024-12-08 14:30:25"
    sample_rate = 1000
    n_channels = 4
    data_length = 50000
    duration = data_length / sample_rate
    file_size = "2.5 MB"
    version = "2.0.0"
    
    recording_info = f"""📊 波形录制信息
📁 文件名: {current_file_name}
🕒 创建时间: {created_time}
📈 采样率: {sample_rate} Hz
📊 通道数: {n_channels}
📏 数据长度: {data_length:,} 点
⏱️ 录制时长: {duration:.1f} 秒
💾 文件大小: {file_size}
📦 版本: {version}
🔴 状态: 录制中..."""
    
    print("录制状态信息显示:")
    print(recording_info)
    print("\n" + "=" * 50 + "\n")
    
    # 模拟预览状态的信息显示
    preview_info = f"""📊 波形预览信息
📁 文件名: {current_file_name}
🕒 创建时间: {created_time}
📈 采样率: {sample_rate} Hz
📊 通道数: {n_channels}
📏 数据长度: {data_length:,} 点
⏱️ 录制时长: {duration:.2f} 秒
📦 版本: {version}
👁️ 状态: 预览模式"""
    
    print("预览状态信息显示:")
    print(preview_info)
    print("\n" + "=" * 50 + "\n")
    
    # 模拟未录制状态的信息显示
    idle_info = """📊 波形信息
⚪ 状态: 未录制
💡 点击"录波使能"开始录制"""
    
    print("未录制状态信息显示:")
    print(idle_info)

if __name__ == "__main__":
    print("=" * 60)
    print("波形信息显示功能测试")
    print("=" * 60)
    
    test_time_formatting()
    print("\n" + "=" * 60 + "\n")
    
    test_file_size_formatting()
    print("\n" + "=" * 60 + "\n")
    
    test_wave_info_display()
    
    print("\n" + "=" * 60)
    print("测试完成！")
    print("=" * 60)
