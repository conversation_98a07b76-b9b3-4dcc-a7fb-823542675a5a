# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'src\ui\dcdc_panel.ui'
#
# Created by: PyQt5 UI code generator 5.15.9
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import QtCore, QtGui, QtWidgets


class Ui_Form(object):
    def setupUi(self, Form):
        Form.setObjectName("Form")
        Form.resize(1139, 1008)
        self.gridLayout_2 = QtWidgets.QGridLayout(Form)
        self.gridLayout_2.setObjectName("gridLayout_2")
        self.SubtitleLabel = SubtitleLabel(Form)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.SubtitleLabel.sizePolicy().hasHeightForWidth())
        self.SubtitleLabel.setSizePolicy(sizePolicy)
        self.SubtitleLabel.setMinimumSize(QtCore.QSize(0, 50))
        self.SubtitleLabel.setMaximumSize(QtCore.QSize(16777215, 50))
        self.SubtitleLabel.setAlignment(QtCore.Qt.AlignCenter)
        self.SubtitleLabel.setObjectName("SubtitleLabel")
        self.gridLayout_2.addWidget(self.SubtitleLabel, 0, 0, 1, 1)
        self.CardWidget = CardWidget(Form)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.CardWidget.sizePolicy().hasHeightForWidth())
        self.CardWidget.setSizePolicy(sizePolicy)
        self.CardWidget.setObjectName("CardWidget")
        self.gridLayout = QtWidgets.QGridLayout(self.CardWidget)
        self.gridLayout.setObjectName("gridLayout")
        self.table_dcdc_param = TableWidget(self.CardWidget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.table_dcdc_param.sizePolicy().hasHeightForWidth())
        self.table_dcdc_param.setSizePolicy(sizePolicy)
        self.table_dcdc_param.setMinimumSize(QtCore.QSize(440, 0))
        font = QtGui.QFont()
        font.setPointSize(12)
        self.table_dcdc_param.setFont(font)
        self.table_dcdc_param.setSelectRightClickedRow(True)
        self.table_dcdc_param.setObjectName("table_dcdc_param")
        self.table_dcdc_param.setColumnCount(0)
        self.table_dcdc_param.setRowCount(0)
        self.gridLayout.addWidget(self.table_dcdc_param, 1, 1, 1, 1)
        self.table_dcdc_fault = TableWidget(self.CardWidget)
        font = QtGui.QFont()
        font.setPointSize(12)
        self.table_dcdc_fault.setFont(font)
        self.table_dcdc_fault.setSelectRightClickedRow(True)
        self.table_dcdc_fault.setObjectName("table_dcdc_fault")
        self.table_dcdc_fault.setColumnCount(0)
        self.table_dcdc_fault.setRowCount(0)
        self.gridLayout.addWidget(self.table_dcdc_fault, 2, 1, 1, 1)
        self.gridLayout_2.addWidget(self.CardWidget, 1, 0, 1, 1)

        self.retranslateUi(Form)
        QtCore.QMetaObject.connectSlotsByName(Form)

    def retranslateUi(self, Form):
        _translate = QtCore.QCoreApplication.translate
        Form.setWindowTitle(_translate("Form", "Form"))
        self.SubtitleLabel.setText(_translate("Form", "DCDC状态监控"))
from qfluentwidgets import CardWidget, SubtitleLabel, TableWidget
