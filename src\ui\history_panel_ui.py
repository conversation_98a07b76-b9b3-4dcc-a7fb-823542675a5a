# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'src\ui\history_panel.ui'
#
# Created by: PyQt5 UI code generator 5.15.9
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import Qt<PERSON><PERSON>, QtGui, QtWidgets


class Ui_Form(object):
    def setupUi(self, Form):
        Form.setObjectName("Form")
        Form.resize(1025, 755)
        self.gridLayout_2 = QtWidgets.QGridLayout(Form)
        self.gridLayout_2.setObjectName("gridLayout_2")
        self.calendar_picker_fault_history = FastCalendarPicker(Form)
        self.calendar_picker_fault_history.setObjectName("calendar_picker_fault_history")
        self.gridLayout_2.addWidget(self.calendar_picker_fault_history, 0, 0, 1, 1)
        self.combo_box_devices = ComboBox(Form)
        self.combo_box_devices.setObjectName("combo_box_devices")
        self.gridLayout_2.addWidget(self.combo_box_devices, 0, 1, 1, 1)
        self.card_fault_history = CardWidget(Form)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.card_fault_history.sizePolicy().hasHeightForWidth())
        self.card_fault_history.setSizePolicy(sizePolicy)
        self.card_fault_history.setObjectName("card_fault_history")
        self.gridLayout = QtWidgets.QGridLayout(self.card_fault_history)
        self.gridLayout.setObjectName("gridLayout")
        self.gridLayout_2.addWidget(self.card_fault_history, 1, 0, 1, 2)

        self.retranslateUi(Form)
        QtCore.QMetaObject.connectSlotsByName(Form)

    def retranslateUi(self, Form):
        _translate = QtCore.QCoreApplication.translate
        Form.setWindowTitle(_translate("Form", "Form"))
from qfluentwidgets import FastCalendarPicker, CardWidget, ComboBox
